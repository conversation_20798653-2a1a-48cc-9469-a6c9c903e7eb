package vn.vinclub.voucher.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.voucher.constant.AppErrorCode;
import vn.vinclub.voucher.dto.client.urbox.request.UrBoxGetGiftDetailReqDto;
import vn.vinclub.voucher.exception.BusinessLogicException;
import vn.vinclub.voucher.service.client.UrBoxProviderClient;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Test to reproduce ConnectionRequestTimeoutException with the current configuration
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("local") // Use your current configuration
public class ConnectionRequestTimeoutTest {

    @Autowired
    private UrBoxProviderClient urBoxProviderClient;

    private static final String GIFT_ID = "4870"; // Use a valid gift ID from your environment
    
    /**
     * Test to reproduce ConnectionRequestTimeoutException by overwhelming the connection pool
     * This test uses the current configuration without any modifications
     */
    @Test
    public void testReproduceConnectionRequestTimeout() throws InterruptedException {
        // Number of concurrent threads - adjust based on your current max connections setting
        // This should be higher than your current connection pool size to reproduce the issue
        int concurrentThreads = 100;
        
        // Total number of requests to make
        int totalRequests = 100;
        
        log.info("Starting test to reproduce ConnectionRequestTimeoutException");
        log.info("Concurrent threads: {}, Total requests: {}", concurrentThreads, totalRequests);
        
        // Create a thread pool with a fixed number of threads
        ExecutorService executorService = Executors.newFixedThreadPool(concurrentThreads);
        
        // CountDownLatch to wait for all requests to complete
        CountDownLatch latch = new CountDownLatch(totalRequests);
        
        // Track successful and failed requests
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger connectionTimeoutCount = new AtomicInteger(0);
        AtomicInteger otherErrorCount = new AtomicInteger(0);
        
        // List to store exceptions
        List<Exception> exceptions = new ArrayList<>();

        // Submit tasks to the executor service
        for (int i = 0; i < totalRequests; i++) {
            executorService.submit(() -> {
                try {
                    // Create request DTO
                    UrBoxGetGiftDetailReqDto reqDto = UrBoxGetGiftDetailReqDto.builder()
                            .id(GIFT_ID)
                            .build();
                    
                    // Make the API call
                    var response = urBoxProviderClient.getGiftDetail(reqDto, "vi");
                    
                    // Check if the response is successful
                    if (response != null && response.isSuccess()) {
                        successCount.incrementAndGet();
                    } else {
                        otherErrorCount.incrementAndGet();
                        log.error("Failed to get gift detail: {}", JsonUtils.toString(response));
                    }
                } catch (BusinessLogicException e) {
                    if (e.getPayload().getCode() == AppErrorCode.EXTERNAL_API_CALL_TIMEOUT.getCode()) {
                        connectionTimeoutCount.incrementAndGet();
                    } else {
                        otherErrorCount.incrementAndGet();
                    }
                    log.error("BusinessLogicException: {}", e.getMessage());
                    exceptions.add(e);
                } catch (Exception e) {
                    // Other exceptions
                    otherErrorCount.incrementAndGet();
                    exceptions.add(e);
                    log.error("Exception: {}", e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }

        // Wait for all requests to complete or timeout after 3 minutes
        boolean completed = latch.await(3, TimeUnit.MINUTES);
        
        // Shutdown the executor service
        executorService.shutdown();
        
        // Log results
        log.info("Test completed: {}", completed ? "All requests completed" : "Timed out waiting for requests");
        log.info("Success count: {}", successCount.get());
        log.info("Connection timeout count: {}", connectionTimeoutCount.get());
        log.info("Other error count: {}", otherErrorCount.get());
        
        // Log the first few exceptions if any occurred
        if (!exceptions.isEmpty()) {
            log.info("First exception: {}", exceptions.get(0).getMessage());
            if (exceptions.size() > 1) {
                log.info("Second exception: {}", exceptions.get(1).getMessage());
            }
            
            // Check if we have any ConnectionRequestTimeoutException
            boolean foundConnectionRequestTimeout = exceptions.stream()
                    .anyMatch(e -> e.getMessage().contains("Connection request timeout"));
            
            log.info("ConnectionRequestTimeoutException found: {}", foundConnectionRequestTimeout);
        }
    }
}
