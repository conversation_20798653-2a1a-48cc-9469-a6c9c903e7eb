package vn.vinclub.voucher.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class CustomUtilTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void testCompareJsonNodes_NullNodes() {
        // Both null
        assertTrue(CustomUtil.compareJsonNodes(null, null));

        // One null, one not null
        JsonNode node = objectMapper.createObjectNode();
        assertFalse(CustomUtil.compareJsonNodes(node, null));
        assertFalse(CustomUtil.compareJsonNodes(null, node));
    }

    @Test
    void testCompareJsonNodes_SimpleValues() {
        // Equal string values
        JsonNode node1 = objectMapper.valueToTree("test");
        JsonNode node2 = objectMapper.valueToTree("test");
        assertTrue(CustomUtil.compareJsonNodes(node1, node2));

        // Different string values
        JsonNode node3 = objectMapper.valueToTree("different");
        assertFalse(CustomUtil.compareJsonNodes(node1, node3));

        // Equal number values
        JsonNode node4 = objectMapper.valueToTree(123);
        JsonNode node5 = objectMapper.valueToTree(123);
        assertTrue(CustomUtil.compareJsonNodes(node4, node5));

        // Different number values
        JsonNode node6 = objectMapper.valueToTree(456);
        assertFalse(CustomUtil.compareJsonNodes(node4, node6));

        // Different types
        assertFalse(CustomUtil.compareJsonNodes(node1, node4));
    }

    @Test
    void testCompareJsonNodes_Objects() {
        // Equal empty objects
        ObjectNode obj1 = objectMapper.createObjectNode();
        ObjectNode obj2 = objectMapper.createObjectNode();
        assertTrue(CustomUtil.compareJsonNodes(obj1, obj2));

        // Equal objects with simple fields
        obj1.put("name", "John");
        obj1.put("age", 30);

        obj2.put("name", "John");
        obj2.put("age", 30);

        assertTrue(CustomUtil.compareJsonNodes(obj1, obj2));

        // Different objects (different field value)
        ObjectNode obj3 = objectMapper.createObjectNode();
        obj3.put("name", "Jane");
        obj3.put("age", 30);

        assertFalse(CustomUtil.compareJsonNodes(obj1, obj3));

        // Different objects (missing field)
        ObjectNode obj4 = objectMapper.createObjectNode();
        obj4.put("name", "John");

        assertFalse(CustomUtil.compareJsonNodes(obj1, obj4));

        // Nested objects - equal
        ObjectNode nested1 = objectMapper.createObjectNode();
        nested1.put("street", "Main St");
        nested1.put("city", "New York");
        obj1.set("address", nested1);

        ObjectNode nested2 = objectMapper.createObjectNode();
        nested2.put("street", "Main St");
        nested2.put("city", "New York");
        obj2.set("address", nested2);

        assertTrue(CustomUtil.compareJsonNodes(obj1, obj2));

        // Nested objects - different
        ObjectNode nested3 = objectMapper.createObjectNode();
        nested3.put("street", "Broadway");
        nested3.put("city", "New York");
        obj3.set("address", nested3);

        assertFalse(CustomUtil.compareJsonNodes(obj1, obj3));
    }

    @Test
    void testCompareJsonNodes_Arrays() {
        // Equal empty arrays
        ArrayNode arr1 = objectMapper.createArrayNode();
        ArrayNode arr2 = objectMapper.createArrayNode();
        assertTrue(CustomUtil.compareJsonNodes(arr1, arr2));

        // Equal arrays with simple values
        arr1.add("item1").add("item2").add("item3");
        arr2.add("item1").add("item2").add("item3");
        assertTrue(CustomUtil.compareJsonNodes(arr1, arr2));

        // Different arrays (different value)
        ArrayNode arr3 = objectMapper.createArrayNode();
        arr3.add("item1").add("different").add("item3");
        assertFalse(CustomUtil.compareJsonNodes(arr1, arr3));

        // Different arrays (different length)
        ArrayNode arr4 = objectMapper.createArrayNode();
        arr4.add("item1").add("item2");
        assertFalse(CustomUtil.compareJsonNodes(arr1, arr4));

        // Arrays with objects - equal
        ObjectNode obj1 = objectMapper.createObjectNode();
        obj1.put("name", "John");
        arr1.add(obj1);

        ObjectNode obj2 = objectMapper.createObjectNode();
        obj2.put("name", "John");
        arr2.add(obj2);

        assertTrue(CustomUtil.compareJsonNodes(arr1, arr2));

        // Arrays with objects - different
        ObjectNode obj3 = objectMapper.createObjectNode();
        obj3.put("name", "Jane");
        arr3.add(obj3);

        assertFalse(CustomUtil.compareJsonNodes(arr1, arr3));
    }

    @Test
    void testMergeJsonNodes_NullNodes() {
        // Both null
        assertNull(CustomUtil.mergeJsonNodes(null, null));

        // One null, one not null
        JsonNode node = objectMapper.createObjectNode();
        assertEquals(node, CustomUtil.mergeJsonNodes(node, null));
        assertEquals(node, CustomUtil.mergeJsonNodes(null, node));
    }

    @Test
    void testMergeJsonNodes_SimpleValues() {
        // Simple values - second takes precedence
        JsonNode node1 = objectMapper.valueToTree("value1");
        JsonNode node2 = objectMapper.valueToTree("value2");
        assertEquals(node2, CustomUtil.mergeJsonNodes(node1, node2));
    }

    @Test
    void testMergeJsonNodes_Objects() {
        // Merge objects with non-overlapping fields
        ObjectNode obj1 = objectMapper.createObjectNode();
        obj1.put("field1", "value1");
        obj1.put("field2", 123);

        ObjectNode obj2 = objectMapper.createObjectNode();
        obj2.put("field3", "value3");
        obj2.put("field4", true);

        JsonNode merged = CustomUtil.mergeJsonNodes(obj1, obj2);
        assertTrue(merged.isObject());
        assertEquals(4, merged.size());
        assertEquals("value1", merged.get("field1").asText());
        assertEquals(123, merged.get("field2").asInt());
        assertEquals("value3", merged.get("field3").asText());
        assertTrue(merged.get("field4").asBoolean());

        // Merge objects with overlapping fields
        ObjectNode obj3 = objectMapper.createObjectNode();
        obj3.put("field1", "updated");
        obj3.put("field3", "new");

        JsonNode merged2 = CustomUtil.mergeJsonNodes(obj1, obj3);
        assertTrue(merged2.isObject());
        assertEquals(3, merged2.size());
        assertEquals("updated", merged2.get("field1").asText());
        assertEquals(123, merged2.get("field2").asInt());
        assertEquals("new", merged2.get("field3").asText());

        // Merge nested objects
        ObjectNode nested1 = objectMapper.createObjectNode();
        nested1.put("nestedField1", "nestedValue1");
        obj1.set("nested", nested1);

        ObjectNode nested2 = objectMapper.createObjectNode();
        nested2.put("nestedField2", "nestedValue2");
        obj3.set("nested", nested2);

        JsonNode merged3 = CustomUtil.mergeJsonNodes(obj1, obj3);
        assertTrue(merged3.isObject());
        assertTrue(merged3.get("nested").isObject());
        assertEquals(2, merged3.get("nested").size());
        assertEquals("nestedValue1", merged3.get("nested").get("nestedField1").asText());
        assertEquals("nestedValue2", merged3.get("nested").get("nestedField2").asText());
    }

    @Test
    void testMergeJsonNodes_Arrays() {
        // Merge arrays
        ArrayNode arr1 = objectMapper.createArrayNode();
        arr1.add("item1").add("item2");

        ArrayNode arr2 = objectMapper.createArrayNode();
        arr2.add("item3").add("item4");

        JsonNode merged = CustomUtil.mergeJsonNodes(arr1, arr2);
        assertTrue(merged.isArray());
        assertEquals(4, merged.size());
        assertEquals("item1", merged.get(0).asText());
        assertEquals("item2", merged.get(1).asText());
        assertEquals("item3", merged.get(2).asText());
        assertEquals("item4", merged.get(3).asText());
    }

    @Test
    void testMergeJsonNodes_MixedTypes() {
        // Different types - second takes precedence
        ObjectNode obj = objectMapper.createObjectNode();
        obj.put("field", "value");

        ArrayNode arr = objectMapper.createArrayNode();
        arr.add("item");

        JsonNode merged1 = CustomUtil.mergeJsonNodes(obj, arr);
        assertTrue(merged1.isArray());
        assertEquals(arr, merged1);

        JsonNode merged2 = CustomUtil.mergeJsonNodes(arr, obj);
        assertTrue(merged2.isObject());
        assertEquals(obj, merged2);
    }

    @Test
    void testMergeJsonNodes_WithReplaceNodes() {
        // Create main object with nested objects
        ObjectNode mainNode = objectMapper.createObjectNode();
        mainNode.put("field1", "value1");

        // Create nested object 1
        ObjectNode nested1 = objectMapper.createObjectNode();
        nested1.put("nestedField1", "nestedValue1");
        nested1.put("nestedField2", "nestedValue2");
        mainNode.set("nested", nested1);

        // Create another nested object
        ObjectNode anotherNested = objectMapper.createObjectNode();
        anotherNested.put("anotherField", "anotherValue");
        mainNode.set("anotherNested", anotherNested);

        // Create update object with different nested structure
        ObjectNode updateNode = objectMapper.createObjectNode();
        updateNode.put("field1", "updatedValue1");

        // Create different nested object
        ObjectNode nested2 = objectMapper.createObjectNode();
        nested2.put("completelyDifferent", "newValue");
        updateNode.set("nested", nested2);

        // Test normal merge (should merge nested objects)
        JsonNode normalMerged = CustomUtil.mergeJsonNodes(mainNode, updateNode);
        assertTrue(normalMerged.isObject());
        assertEquals("updatedValue1", normalMerged.get("field1").asText());
        assertTrue(normalMerged.get("nested").isObject());
        assertEquals(3, normalMerged.get("nested").size()); // Combined fields from both nested objects
        assertEquals("nestedValue1", normalMerged.get("nested").get("nestedField1").asText());
        assertEquals("nestedValue2", normalMerged.get("nested").get("nestedField2").asText());
        assertEquals("newValue", normalMerged.get("nested").get("completelyDifferent").asText());
        assertTrue(normalMerged.get("anotherNested").isObject());

        // Test with replace for "nested" node (should replace instead of merge)
        String[] replaceNodes = new String[]{"nested"};
        JsonNode replaceMerged = CustomUtil.mergeJsonNodes(mainNode, updateNode, replaceNodes);
        assertTrue(replaceMerged.isObject());
        assertEquals("updatedValue1", replaceMerged.get("field1").asText());
        assertTrue(replaceMerged.get("nested").isObject());
        assertEquals(1, replaceMerged.get("nested").size()); // Only fields from the update node
        assertEquals("newValue", replaceMerged.get("nested").get("completelyDifferent").asText());
        assertFalse(replaceMerged.get("nested").has("nestedField1")); // Should not have fields from original
        assertFalse(replaceMerged.get("nested").has("nestedField2")); // Should not have fields from original
        assertTrue(replaceMerged.get("anotherNested").isObject());

        // Test with multiple replace nodes
        ObjectNode updateNode2 = objectMapper.createObjectNode();
        ObjectNode anotherNested2 = objectMapper.createObjectNode();
        anotherNested2.put("newField", "newValue");
        updateNode2.set("anotherNested", anotherNested2);

        String[] multipleReplaceNodes = new String[]{"nested", "anotherNested"};
        JsonNode multiReplaceMerged = CustomUtil.mergeJsonNodes(mainNode, updateNode2, multipleReplaceNodes);
        assertTrue(multiReplaceMerged.isObject());
        assertEquals("value1", multiReplaceMerged.get("field1").asText());
        assertTrue(multiReplaceMerged.get("anotherNested").isObject());
        assertEquals(1, multiReplaceMerged.get("anotherNested").size());
        assertEquals("newValue", multiReplaceMerged.get("anotherNested").get("newField").asText());
        assertFalse(multiReplaceMerged.get("anotherNested").has("anotherField"));
    }

    @Test
    void testHashMD5() {
        // Test null input
        assertNull(CustomUtil.hashMD5(null));

        // Test empty string
        // MD5 of empty string is d41d8cd98f00b204e9800998ecf8427e
        assertEquals("d41d8cd98f00b204e9800998ecf8427e", CustomUtil.hashMD5(""));

        // Test regular string
        // MD5 of "test" is 098f6bcd4621d373cade4e832627b4f6
        assertEquals("098f6bcd4621d373cade4e832627b4f6", CustomUtil.hashMD5("test"));

        // Test string with special characters
        // MD5 of "test@123!#" is a9371cbdeafaa90c645c2b5bd30b8652
        assertEquals("a9371cbdeafaa90c645c2b5bd30b8652", CustomUtil.hashMD5("test@123!#"));

        // Test longer string
        // MD5 of "The quick brown fox jumps over the lazy dog" is 9e107d9d372bb6826bd81d3542a419d6
        assertEquals("9e107d9d372bb6826bd81d3542a419d6", CustomUtil.hashMD5("The quick brown fox jumps over the lazy dog"));
    }
}
