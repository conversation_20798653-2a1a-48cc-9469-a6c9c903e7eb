package vn.vinclub.voucher.kafka;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.voucher.dto.event.internal.ProviderVoucherListingStatusChangeEvent;
import vn.vinclub.voucher.dto.event.internal.ProviderVoucherPurchaseRequestEvent;
import vn.vinclub.voucher.exception.IgnoreProcessingException;
import vn.vinclub.voucher.service.factory.ProviderIntegrationServiceFactory;

@Service
@Slf4j
@RequiredArgsConstructor
public class KafkaConsumer {

    private final BaseJsonUtils baseJsonUtils;

    private final ProviderIntegrationServiceFactory providerIntegrationServiceFactory;

    @KafkaListener(topics = "${kafka.provider_voucher_purchase_request.topic.name}",
            groupId = "${kafka.provider_voucher_purchase_request.group.name}",
            containerFactory = "kafkaIntListenerContainerFactory",
            autoStartup = "${kafka.voucher_purchase_request.enabled:true}"
    )
    public void consumeVoucherPurchaseRequestEvent(ConsumerRecord<String, String> record, Acknowledgment ack) {
        try (Profiler p = new Profiler(getClass(), "consumeVoucherPurchaseRequestEvent")) {
            log.info("[Kafka] Consume message - topic={} - partition={}, Offset={} ", record.topic(), record.partition(), record.offset());
            String message = record.value();

            var event = baseJsonUtils.toObjectOrThrow(message, ProviderVoucherPurchaseRequestEvent.class);

            providerIntegrationServiceFactory.getService(event.getProviderCode()).purchaseVoucher(event);

            ack.acknowledge();
            log.info("[Kafka] Committed message - topic={}, partition={}, Offset={} ", record.topic(), record.partition(), record.offset());
        } catch (IgnoreProcessingException ex) {
            ack.acknowledge();
            log.warn("[Kafka] Ignore message - topic={}, partition={}, Offset={} - {}", record.topic(), record.partition(), record.offset(), ex.getMessage());
        } catch (Exception ex) {
            log.error("[Kafka] process message error {} - topic={}, partition={}, Offset={}", ex.getMessage(), record.topic(), record.partition(), record.offset(), ex);
        }
    }

    @KafkaListener(topics = "${kafka.provider_voucher_listing_status_change.topic.name}",
            groupId = "${kafka.provider_voucher_listing_status_change.group.name}",
            containerFactory = "kafkaIntListenerContainerFactory",
            autoStartup = "${kafka.voucher_listing_status_change.enabled:true}"
    )
    public void consumeVoucherListingStatusChangeEvent(ConsumerRecord<String, String> record, Acknowledgment ack) {
        try (Profiler p = new Profiler(getClass(), "consumeVoucherListingStatusChangeEvent")) {
            log.info("[Kafka] Consume message - topic={} - partition={}, Offset={} ", record.topic(), record.partition(), record.offset());
            String message = record.value();

            var event = baseJsonUtils.toObjectOrThrow(message, ProviderVoucherListingStatusChangeEvent.class);

            if (Boolean.TRUE.equals(event.getVoucherDetailChanged())) {
                providerIntegrationServiceFactory.getService(event.getProviderCode()).invalidateProviderVoucherListingDetailCache(event.getVoucherListingId());
            }

            ack.acknowledge();
            log.info("[Kafka] Committed message - topic={}, partition={}, Offset={} ", record.topic(), record.partition(), record.offset());
        } catch (IgnoreProcessingException ex) {
            ack.acknowledge();
            log.warn("[Kafka] Ignore message - topic={}, partition={}, Offset={} - {}", record.topic(), record.partition(), record.offset(), ex.getMessage());
        } catch (Exception ex) {
            log.error("[Kafka] process message error {} - topic={}, partition={}, Offset={}", ex.getMessage(), record.topic(), record.partition(), record.offset(), ex);
        }
    }

}
