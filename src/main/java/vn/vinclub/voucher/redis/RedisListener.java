package vn.vinclub.voucher.redis;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.convert.ReadingConverter;
import org.springframework.stereotype.Component;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.voucher.constant.AppConst;
import vn.vinclub.voucher.dto.ChangeMsgDto;
import vn.vinclub.voucher.service.ProviderMerchantService;
import vn.vinclub.voucher.service.ProviderService;
import vn.vinclub.voucher.service.ProviderVoucherListingService;

@Component
@Slf4j
@ReadingConverter
@RequiredArgsConstructor
public class RedisListener {

    private final ProviderService providerService;
    private final ProviderMerchantService providerMerchantService;
    private final ProviderVoucherListingService providerVoucherListingService;

    @Value("${redis.channel.change}")
    private String channel;

    private final RedissonClient redissonClient;

    private final BaseJsonUtils baseJsonUtils;

    @PostConstruct
    public void init() {
        redissonClient.getTopic(channel).addListener(String.class, (channel, msg) -> {
            log.info("onMessage: {}", msg);
            var data = baseJsonUtils.toObject(msg, ChangeMsgDto.class);
            String key = data.getMsgKey();
            Long id = data.getId();
            if (AppConst.RedisMessage.CHANGE_VOUCHER_PROVIDER.equalsIgnoreCase(key)) {
                String code = data.getCode();
                providerService.invalidateCache(id, code);
            }
            if (AppConst.RedisMessage.CHANGE_PROVIDER_MERCHANT.equalsIgnoreCase(key)) {
                providerMerchantService.invalidateCache(id);
            }
            if (AppConst.RedisMessage.CHANGE_PROVIDER_VOUCHER_LISTING.equalsIgnoreCase(key)) {
                providerVoucherListingService.invalidateCache(id);
            }
        });
    }
}
