package vn.vinclub.voucher.redis;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.voucher.dto.ChangeMsgDto;


@Component
@RequiredArgsConstructor
public class RedisPublish {

    @Value("${redis.channel.change}")
    private String channelUpdate;

    private final RedissonClient redissonClient;

    private final BaseJsonUtils baseJsonUtils;

    private RTopic topic;

    @PostConstruct
    public void init() {
        topic = redissonClient.getTopic(channelUpdate);
    }

    public void sendChange(String msgKey, Long id) {
        var data = ChangeMsgDto.builder()
                .msgKey(msgKey)
                .id(id)
                .build();
        topic.publish(baseJsonUtils.toString(data));
    }

    public void sendChange(String msgKey, Long id, String code) {
        var data = ChangeMsgDto.builder()
                .msgKey(msgKey)
                .id(id)
                .code(code)
                .build();
        topic.publish(baseJsonUtils.toString(data));
    }

}
