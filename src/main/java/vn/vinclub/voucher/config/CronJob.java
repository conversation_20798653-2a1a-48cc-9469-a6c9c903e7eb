package vn.vinclub.voucher.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;
import vn.vinclub.voucher.service.EventService;
import vn.vinclub.voucher.service.ProviderIntegrationService;
import vn.vinclub.voucher.service.factory.ProviderIntegrationServiceFactory;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
@RequiredArgsConstructor
public class CronJob {

    private final EventService eventService;
    private final ProviderIntegrationServiceFactory providerIntegrationServiceFactory;

    @Scheduled(initialDelay = 30000, fixedDelay = 30 * 1000) // every 30 seconds
    @SchedulerLock(name = "voucher_svc:retryProcessPendingOutbox", lockAtLeastFor = "30s", lockAtMostFor = "10m")
    public void retryProcessPendingOutbox() {
        log.info("Start retryProcessPendingOutbox...........");
        eventService.processPendingOutboxEvent();
        log.info("End retryProcessPendingOutbox!");
    }

    @Scheduled(initialDelay = 45000, fixedDelay = 30 * 60 * 1000) // every 30 minutes
    @SchedulerLock(name = "voucher_svc:retryTimeoutTransaction", lockAtLeastFor = "2m", lockAtMostFor = "60m")
    public void retryTimeoutTransaction() {
        log.info("Start retryTimeoutTransaction...........");
        providerIntegrationServiceFactory.getActiveServices().forEach(ProviderIntegrationService::retryTimeoutTransaction);
        log.info("End retryTimeoutTransaction!");
    }

    @Scheduled(initialDelay = 60000, fixedDelay = 5 * 60 * 1000) // every 5 minutes
    @SchedulerLock(name = "voucher_svc:syncProviderVoucherListingStatusJob", lockAtLeastFor = "2m", lockAtMostFor = "30m")
    public void syncProviderVoucherListingStatusJob() {
        log.info("Start syncProviderVoucherListingStatusJob...........");
        providerIntegrationServiceFactory.getActiveServices().forEach(ProviderIntegrationService::syncProviderVoucherListing);
        log.info("End syncProviderVoucherListingStatusJob!");
    }

    @Scheduled(cron = "0 0 1,13 * * *") // At 04:00 AM
    @SchedulerLock(name = "voucher_svc:syncProviderMerchantJob", lockAtLeastFor = "2m", lockAtMostFor = "10m")
    public void syncProviderMerchantJob() {
        log.info("Start syncProviderMerchantJob...........");
        providerIntegrationServiceFactory.getActiveServices().forEach(ProviderIntegrationService::syncProviderMerchant);
        log.info("End syncProviderMerchantJob!");
    }

    @Scheduled(cron = "0 0 3 * * *") //  at 3:00 AM
    @SchedulerLock(name = "voucher_svc:syncPurchasedVoucherStatusJob", lockAtLeastFor = "5m", lockAtMostFor = "180m")
    public void syncPurchasedVoucherStatusJob() {
        log.info("Start syncPurchasedVoucherStatusJob...........");
        providerIntegrationServiceFactory.getActiveServices().forEach(ProviderIntegrationService::syncPurchasedVoucherStatus);
        log.info("End syncPurchasedVoucherStatusJob!");
    }

}
