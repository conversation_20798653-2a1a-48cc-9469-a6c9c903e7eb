package vn.vinclub.voucher.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.hc.client5.http.config.ConnectionConfig;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.core5.util.TimeValue;
import org.apache.hc.core5.util.Timeout;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.web.client.RestTemplate;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.voucher.constant.AppConst;
import vn.vinclub.voucher.interceptor.RestTemplateLoggingInterceptor;
import vn.vinclub.voucher.service.DistributedIdGenerator;
import vn.vinclub.voucher.service.impl.SnowflakeNodeIdBasedRedis;

import java.util.ArrayList;
import java.util.List;

@Configuration
@EnableRetry
@RequiredArgsConstructor
public class AppConfig {

    @Value("${vinclub.logging.level.client.RestTemplate:}")
    private String restTemplateLogLevel;

    @Value("${provider_integration.urbox.connect_timeout:3000}")
    private int urboxConnectTimeout;

    @Value("${provider_integration.urbox.response_timeout:5000}")
    private int urboxResponseTimeout;

    @Value("${provider_integration.urbox.max_connections:100}")
    private int urboxMaxConnections;

    @Value("${default.connect_timeout:30000}")
    private int defaultConnectTimeout;

    @Value("${default.response_timeout:30000}")
    private int defaultResponseTimeout;

    @Value("${default.max_connections:50}")
    private int defaultMaxConnections;

    @Value("${default.max_connections_per_route:10}")
    private int defaultMaxConnectionsPerRoute;

    @Value("${default.connection_idle_time:300000}")
    private int defaultConnectionIdleTime;

    @Value("${default.connection_request_timeout:30000}")
    private int defaultConnectionRequestTimeout;

    private final RedissonClient redissonClient;
    private final ObjectMapper mapper;

    /**
     * Default RestTemplate bean for general use
     */
    @Bean
    @Primary
    public RestTemplate defaultRestTemplate() {
        // Configure request timeouts
        RequestConfig requestConfig = RequestConfig.custom()
                .setResponseTimeout(Timeout.ofMilliseconds(defaultResponseTimeout))
                .setConnectionRequestTimeout(Timeout.ofMilliseconds(defaultConnectionRequestTimeout))
                .build();

        // Configure connection timeouts
        ConnectionConfig connectionConfig = ConnectionConfig.custom()
                .setConnectTimeout(Timeout.ofMilliseconds(defaultConnectTimeout))
                .setSocketTimeout(Timeout.ofMilliseconds(defaultConnectTimeout))
                .build();

        // Create connection pool manager with max connections for concurrency
        PoolingHttpClientConnectionManager connectionManager = PoolingHttpClientConnectionManagerBuilder.create()
                .setDefaultConnectionConfig(connectionConfig)
                .setMaxConnTotal(defaultMaxConnections)
                .setMaxConnPerRoute(defaultMaxConnectionsPerRoute)
                .build();

        // Create HttpClient with the configured timeouts and connection pool
        CloseableHttpClient httpClient = HttpClientBuilder.create()
                .setDefaultRequestConfig(requestConfig)
                .setConnectionManager(connectionManager)
                .evictIdleConnections(TimeValue.ofMilliseconds(defaultConnectionIdleTime))
                .build();

        // Create request factory with the configured HttpClient
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);

        return configureRestTemplate(requestFactory);
    }

    /**
     * Specialized RestTemplate for UrBox provider with specific timeout and concurrency settings
     */
    @Bean
    @Qualifier("urboxRestTemplate")
    public RestTemplate urboxRestTemplate() {
        // Configure request timeouts for UrBox
        RequestConfig requestConfig = RequestConfig.custom()
                .setResponseTimeout(Timeout.ofMilliseconds(urboxResponseTimeout))
                .setConnectionRequestTimeout(Timeout.ofMilliseconds(defaultConnectionRequestTimeout))
                .build();

        // Configure connection timeouts for UrBox
        ConnectionConfig connectionConfig = ConnectionConfig.custom()
                .setConnectTimeout(Timeout.ofMilliseconds(urboxConnectTimeout))
                .setSocketTimeout(Timeout.ofMilliseconds(urboxResponseTimeout))
                .build();

        // Create connection pool manager with max connections for concurrency
        PoolingHttpClientConnectionManager connectionManager = PoolingHttpClientConnectionManagerBuilder.create()
                .setDefaultConnectionConfig(connectionConfig)
                .setMaxConnTotal(urboxMaxConnections)
                .setMaxConnPerRoute(urboxMaxConnections)
                .build();

        // Create HttpClient with the configured timeouts and connection pool
        CloseableHttpClient httpClient = HttpClientBuilder.create()
                .setDefaultRequestConfig(requestConfig)
                .setConnectionManager(connectionManager)
                .evictIdleConnections(TimeValue.ofMilliseconds(defaultConnectionIdleTime))
                .build();

        // Create request factory with the configured HttpClient
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);

        return configureRestTemplate(requestFactory);
    }

    /**
     * Helper method to configure a RestTemplate with common settings
     */
    private RestTemplate configureRestTemplate(HttpComponentsClientHttpRequestFactory requestFactory) {
        RestTemplate restTemplate = new RestTemplate();
        if ("DEBUG".equalsIgnoreCase(restTemplateLogLevel)) {
            restTemplate.setRequestFactory(new BufferingClientHttpRequestFactory(requestFactory));

            List<ClientHttpRequestInterceptor> interceptors = restTemplate.getInterceptors();
            if (CollectionUtils.isEmpty(interceptors)) {
                interceptors = new ArrayList<>();
            }
            interceptors.add(new RestTemplateLoggingInterceptor());

            restTemplate.setInterceptors(interceptors);
        } else {
            restTemplate.setRequestFactory(requestFactory);
        }

        return restTemplate;
    }

    @Bean(AppConst.IdGenerator.PROVIDER_URBOX_TRANSACTION_ID)
    public DistributedIdGenerator providerUrBoxTransactionIdGenerator() {
        return new SnowflakeNodeIdBasedRedis(redissonClient, "voucher-svc-provider-urbox-transaction-id-gen", "VCXUB");
    }

    @Bean
    public BaseJsonUtils defaultBaseJsonUtils() {
        return new BaseJsonUtils(mapper);
    }
}
