package vn.vinclub.voucher.service;

import jakarta.annotation.PostConstruct;
import vn.vinclub.voucher.dto.event.internal.ProviderVoucherPurchaseRequestEvent;
import vn.vinclub.voucher.dto.provider.voucher.ProviderVoucherListingDetailsDto;
import vn.vinclub.voucher.dto.provider.voucher.VoucherPurchasedDto;

public interface ProviderIntegrationService {
    // region: initialize
    @PostConstruct
    default void init() {
        initProvider();
    }

    /**
     * Initialize provider data
     */
    void initProvider();
    // endregion

    // region: search provider data

    boolean isActiveProvider();

    boolean isConfiguredProvider();

    String getProviderCode();

    /**
     * Get provider voucher listing detail
     *
     * @param providerVoucherListingId provider voucher listing id
     * @param voucherListingId voucher listing id
     * @return provider voucher detail
     */
    ProviderVoucherListingDetailsDto getProviderVoucherListingDetail(String providerVoucherListingId, Long voucherListingId);

    void invalidateProviderVoucherListingDetailCache(Long providerVoucherListingId);
    // endregion

    // region: purchase voucher

    /**
     * Purchase voucher
     *
     * @param event purchase request event
     */
    void purchaseVoucher(ProviderVoucherPurchaseRequestEvent event);

    /**
     * revoke voucher
     *
     * @param voucherImportId voucher import id (Mã yêu cầu import mã voucher)
     * @param voucherCode     voucher code (voucher muốn hủy)
     * @return voucher purchased info
     */
    VoucherPurchasedDto rollbackVoucher(String voucherImportId, String voucherCode);

    /**
     * retry timeout transaction
     */
    void retryTimeoutTransaction();
    // endregion

    // region: sync data

    /**
     * Sync provider merchant data
     */
    void syncProviderMerchant();

    /**
     * Sync provider voucher listing data
     */
    void syncProviderVoucherListing();

    /**
     * Sync purchased voucher status (Redeemed, Expired, Cancelled,...)
     */
    void syncPurchasedVoucherStatus();
    // endregion
}
