package vn.vinclub.voucher.service.impl;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.uuid.Generators;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.voucher.constant.AppErrorCode;
import vn.vinclub.voucher.dto.event.internal.ProviderVoucherPurchaseResultEvent;
import vn.vinclub.voucher.dto.provider.voucher.ProviderVoucherPickupReqDto;
import vn.vinclub.voucher.enums.VoucherPurchaseRequestStatusEnum;
import vn.vinclub.voucher.exception.BusinessLogicException;
import vn.vinclub.voucher.mapper.VoucherPurchaseMapper;
import vn.vinclub.voucher.model.VoucherPurchaseRequestHistory;
import vn.vinclub.voucher.model.VoucherPurchased;
import vn.vinclub.voucher.repository.VoucherPurchaseRequestHistoryRepository;
import vn.vinclub.voucher.service.EventService;
import vn.vinclub.voucher.service.ProviderService;
import vn.vinclub.voucher.service.VoucherPurchaseRequestHistoryService;
import vn.vinclub.voucher.service.VoucherPurchasedService;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
@RequiredArgsConstructor
public class VoucherPurchaseRequestHistoryServiceImpl extends BaseService implements VoucherPurchaseRequestHistoryService {
    private final VoucherPurchaseRequestHistoryRepository voucherPurchaseRequestHistoryRepository;
    private final ProviderService providerService;
    private final VoucherPurchasedService voucherPurchasedService;
    private final EventService eventService;

    @Override
    public VoucherPurchaseRequestHistory findByVoucherImportId(String voucherImportId) {
        try (var p = new Profiler(getClass(), "findByVoucherImportId")) {
            if (Objects.isNull(voucherImportId)) {
                log.error("Invalid voucher import id: {}", voucherImportId);
                throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, VoucherPurchaseRequestHistory.Fields.voucherImportId);
            }
            return voucherPurchaseRequestHistoryRepository.findByVoucherImportId(voucherImportId);
        }
    }

    @Override
    public VoucherPurchaseRequestHistory initPurchaseRequest(ProviderVoucherPickupReqDto req) {
        try (var p = new Profiler(getClass(), "initPurchaseRequest")) {
            var provider = providerService.getActiveById(req.getProviderId());
            var existingRequest = findByVoucherImportId(req.getVoucherImportId());
            if (Objects.nonNull(existingRequest) && VoucherPurchaseRequestStatusEnum.COMPLETED.equals(existingRequest.getStatus())) {
                log.error("Request purchase voucher with voucherImportId {} already processed!", req.getVoucherImportId());
                throw new BusinessLogicException(VoucherPurchaseMapper.INSTANCE.toPickupResponseDto(existingRequest), AppErrorCode.REQUEST_PROCESSED, VoucherPurchaseRequestHistory.Fields.voucherImportId, req.getVoucherImportId());
            }

            if (Objects.nonNull(existingRequest) && VoucherPurchaseRequestStatusEnum.PROCESSING.equals(existingRequest.getStatus())) {
                log.info("Request purchase voucher with voucherImportId {} are processing!", req.getVoucherImportId());
                throw new BusinessLogicException(VoucherPurchaseMapper.INSTANCE.toPickupResponseDto(existingRequest), AppErrorCode.REQUEST_PROCESSING, VoucherPurchaseRequestHistory.Fields.voucherImportId, req.getVoucherImportId());
            }

            // validate quantity
            if (req.getCurrentQuantity() < req.getQuantity()) {
                log.error("Out of stock for providerVoucherListingId: {}. Available quantity: {}, requested quantity: {}", req.getProviderVoucherListingId(), req.getCurrentQuantity(), req.getQuantity());
                throw new BusinessLogicException(AppErrorCode.OUT_OF_STOCK);
            }

            VoucherPurchaseRequestHistory requestHistory = VoucherPurchaseRequestHistory.builder()
                    .providerId(provider.getId())
                    .providerCode(provider.getCode())
                    .voucherImportId(req.getVoucherImportId())
                    .voucherListingId(req.getVoucherListingId())
                    .requestQuantity(req.getQuantity())
                    .purchasedQuantity(0)
                    .status(VoucherPurchaseRequestStatusEnum.PROCESSING)
                    .metadata(req.getMetadata())
                    .build();

            var outboxId = Generators.timeBasedEpochGenerator().generate().toString();
            TransactionStatus tx = null;

            // persist transaction history with outbox event
            try (var p1 = new Profiler(getClass(), "initPurchaseRequest - transaction")) {
                DefaultTransactionDefinition def = new DefaultTransactionDefinition();
                def.setPropagationBehavior(DefaultTransactionDefinition.PROPAGATION_REQUIRES_NEW);
                tx = transactionManager.getTransaction(def);

                requestHistory = voucherPurchaseRequestHistoryRepository.save(requestHistory);

                eventService.persistOutboxEvent(
                        VoucherPurchaseMapper.INSTANCE.toPurchaseRequestEvent(requestHistory),
                        outboxId
                );

                transactionManager.commit(tx);
            } catch (Exception e) {
                log.error("Error while processing purchase request: {}", req.getVoucherImportId(), e);
                throw e;
            } finally {
                if (Objects.nonNull(tx) && !tx.isCompleted()) {
                    transactionManager.rollback(tx);
                }
            }
            // send outbox event
            try {
                eventService.sendOutboxEvent(outboxId, null);
            } catch (Exception e) {
                log.error("Error while sending outbox event: {}", outboxId, e);
            }

            return requestHistory;
        }
    }

    @Override
    @Transactional(Transactional.TxType.REQUIRES_NEW)
    public VoucherPurchaseRequestHistory processPurchaseRequest(VoucherPurchaseRequestHistory voucherPurchaseRequest, List<VoucherPurchased> purchasedVouchers) {
        try (var p = new Profiler(getClass(), "processPurchaseRequest")) {
            var history = getProcessingRequest(voucherPurchaseRequest.getVoucherImportId());

            var savedVouchers = voucherPurchasedService.saveMultiple(purchasedVouchers);
            history.setPurchasedQuantity(history.getPurchasedQuantity() + savedVouchers.size());
            return voucherPurchaseRequestHistoryRepository.save(history);
        }
    }

    @Override
    public void completePurchaseRequest(VoucherPurchaseRequestHistory voucherPurchaseRequest, Integer errorCode, String errorMessage) {
        try (Profiler p = new Profiler(getClass(), "completePurchaseRequest")) {
            var history = getProcessingRequest(voucherPurchaseRequest.getVoucherImportId());

            // Cứ mua được voucher thì xem là thành công cho dù không đủ số lượng
            if (history.getPurchasedQuantity() > 0) {
                errorCode = AppErrorCode.SUCCESS.getCode();
                errorMessage = AppErrorCode.SUCCESS.getMessage();
            }

            var outboxId = Generators.timeBasedEpochGenerator().generate().toString();
            TransactionStatus tx = null;

            // persist transaction history with outbox event
            try (var p1 = new Profiler(getClass(), "completePurchaseRequest - transaction")) {
                DefaultTransactionDefinition def = new DefaultTransactionDefinition();
                def.setPropagationBehavior(DefaultTransactionDefinition.PROPAGATION_REQUIRES_NEW);
                tx = transactionManager.getTransaction(def);

                history.setStatus(VoucherPurchaseRequestStatusEnum.COMPLETED);
                voucherPurchaseRequestHistoryRepository.save(history);

                eventService.persistOutboxEvent(
                        ProviderVoucherPurchaseResultEvent.builder()
                                .voucherImportId(history.getVoucherImportId())
                                .providerId(history.getProviderId())
                                .providerCode(history.getProviderCode())
                                .voucherListingId(history.getVoucherListingId())
                                .requestQuantity(history.getRequestQuantity())
                                .metadata((ObjectNode) history.getMetadata())
                                .purchasedQuantity(history.getPurchasedQuantity())
                                .message(errorMessage)
                                .code(errorCode)
                                .build(),
                        outboxId
                );
                transactionManager.commit(tx);
            } catch (Exception e) {
                log.error("Error while processing purchase request failed: {}", voucherPurchaseRequest.getVoucherImportId(), e);
                throw e;
            } finally {
                if (Objects.nonNull(tx) && !tx.isCompleted()) {
                    transactionManager.rollback(tx);
                }
            }

            // send outbox event
            try {
                eventService.sendOutboxEvent(outboxId, null);
            } catch (Exception e) {
                log.error("Error while sending outbox event: {}", outboxId, e);
            }
        }
    }

    private VoucherPurchaseRequestHistory getProcessingRequest(String voucherImportId) {
        try (var p = new Profiler(getClass(), "getProcessingRequest")) {
            var history = voucherPurchaseRequestHistoryRepository.findByVoucherImportId(voucherImportId);
            if (Objects.isNull(history)) {
                log.error("Voucher purchase request not found: {}", voucherImportId);
                throw new BusinessLogicException(AppErrorCode.NOT_FOUND, VoucherPurchaseRequestHistory.NAME, VoucherPurchaseRequestHistory.Fields.voucherImportId, voucherImportId);
            }

            if (VoucherPurchaseRequestStatusEnum.COMPLETED.equals(history.getStatus())) {
                log.error("Request purchase voucher with voucherImportId {} already processed!", voucherImportId);
                throw new BusinessLogicException(history, AppErrorCode.REQUEST_PROCESSED, VoucherPurchaseRequestHistory.Fields.voucherImportId, voucherImportId);
            }
            return history;
        }
    }
}
