package vn.vinclub.voucher.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.vinclub.voucher.dto.provider.ProviderCreateDto;
import vn.vinclub.voucher.dto.provider.ProviderFilterDto;
import vn.vinclub.voucher.dto.provider.ProviderUpdateDto;
import vn.vinclub.voucher.model.Provider;

import java.security.PrivateKey;
import java.security.PublicKey;

public interface ProviderService {

    Provider create(ProviderCreateDto createDto);

    Provider update(Long id, ProviderUpdateDto updateDto);

    boolean delete(Long id);

    Provider getById(Long id);

    Provider getByCode(String code);

    Provider getActiveById(Long id);

    Provider getActiveByCode(String code);

    Page<Provider> filter(ProviderFilterDto filter, Pageable pageable);

    boolean existsByCode(String code);

    PublicKey getPublicKeyByCode(String code);

    PrivateKey getPrivateKeyByCode(String code);

    void invalidateCache(Long id, String code);
}
