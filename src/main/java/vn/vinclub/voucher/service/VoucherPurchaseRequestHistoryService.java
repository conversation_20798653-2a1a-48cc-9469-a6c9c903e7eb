package vn.vinclub.voucher.service;

import vn.vinclub.voucher.dto.provider.voucher.ProviderVoucherPickupReqDto;
import vn.vinclub.voucher.model.VoucherPurchaseRequestHistory;
import vn.vinclub.voucher.model.VoucherPurchased;

import java.util.List;

public interface VoucherPurchaseRequestHistoryService {

    VoucherPurchaseRequestHistory findByVoucherImportId(String voucherImportId);

    VoucherPurchaseRequestHistory initPurchaseRequest(ProviderVoucherPickupReqDto req);

    VoucherPurchaseRequestHistory processPurchaseRequest(VoucherPurchaseRequestHistory voucherPurchaseRequest, List<VoucherPurchased> purchasedVouchers);

    void completePurchaseRequest(VoucherPurchaseRequestHistory voucherPurchaseRequest, Integer errorCode, String errorMessage);
}
