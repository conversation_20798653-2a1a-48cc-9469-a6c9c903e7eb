package vn.vinclub.voucher.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Iterator;
import java.util.List;


@Slf4j
@RequiredArgsConstructor
public class CustomUtil {

    public static String cutOffString(String str, int length) {
        if (str == null || str.length() <= length) {
            return str;
        }
        return str.substring(0, length);
    }

    public static <T> Page<T> toPage(List<T> list, Pageable pageable) {
        if (pageable.isUnpaged()) {
            return new PageImpl<>(list); // Return the entire list as a single page
        }
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), list.size());
        return new PageImpl<>(list.subList(start, end), pageable, list.size());
    }

    /**
     * Compares two JsonNode objects for equality.
     * This method performs a deep comparison of the structure and values of the JsonNodes.
     *
     * @param node1 the first JsonNode to compare
     * @param node2 the second JsonNode to compare
     * @return true if the JsonNodes are equal, false otherwise
     */
    public static boolean compareJsonNodes(JsonNode node1, JsonNode node2) {
        if (node1 == null && node2 == null) {
            return true;
        }

        if (node1 == null || node2 == null) {
            return false;
        }

        // Check if node types are the same
        if (node1.getNodeType() != node2.getNodeType()) {
            return false;
        }

        // Handle different node types
        if (node1.isObject()) {
            // Compare object nodes
            if (node1.size() != node2.size()) {
                return false;
            }

            Iterator<String> fieldNames = node1.fieldNames();
            while (fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                if (!node2.has(fieldName) || !compareJsonNodes(node1.get(fieldName), node2.get(fieldName))) {
                    return false;
                }
            }
            return true;
        } else if (node1.isArray()) {
            // Compare array nodes
            if (node1.size() != node2.size()) {
                return false;
            }

            for (int i = 0; i < node1.size(); i++) {
                if (!compareJsonNodes(node1.get(i), node2.get(i))) {
                    return false;
                }
            }
            return true;
        } else {
            // Compare value nodes (string, number, boolean, null)
            return node1.equals(node2);
        }
    }

    /**
     * Merges two JsonNode objects.
     * This method performs a deep merge of the structure and values of the JsonNodes.
     * If there are conflicting fields, the values from the second JsonNode take precedence.
     *
     * @param mainNode the base JsonNode
     * @param updateNode the JsonNode to merge into the base
     * @return a new JsonNode containing the merged result
     */
    public static JsonNode mergeJsonNodes(JsonNode mainNode, JsonNode updateNode) {
        return mergeJsonNodes(mainNode, updateNode, null);
    }

    /**
     * Merges two JsonNode objects with option to replace specific nodes instead of merging.
     * This method performs a deep merge of the structure and values of the JsonNodes.
     * If there are conflicting fields, the values from the second JsonNode take precedence.
     * For fields specified in replaceNodeNames, the values are replaced instead of merged.
     *
     * @param mainNode the base JsonNode
     * @param updateNode the JsonNode to merge into the base
     * @param replaceNodeNames array of node names that should be replaced instead of merged
     * @return a new JsonNode containing the merged result
     */
    public static JsonNode mergeJsonNodes(JsonNode mainNode, JsonNode updateNode, String...replaceNodeNames) {
        if (mainNode == null) {
            return updateNode;
        }
        if (updateNode == null) {
            return mainNode;
        }

        // If both nodes are objects, merge them
        if (mainNode.isObject() && updateNode.isObject()) {
            ObjectNode result = ((ObjectNode) mainNode).deepCopy();

            Iterator<String> fieldNames = updateNode.fieldNames();
            while (fieldNames.hasNext()) {
                String fieldName = fieldNames.next();
                JsonNode updateValue = updateNode.get(fieldName);

                // Check if this field should be replaced instead of merged
                boolean shouldReplace = false;
                if (replaceNodeNames != null) {
                    for (String nodeName : replaceNodeNames) {
                        if (fieldName.equals(nodeName)) {
                            shouldReplace = true;
                            break;
                        }
                    }
                }

                // If the field exists in the main node and both are objects, and it's not in the replace list, recursively merge
                if (!shouldReplace && result.has(fieldName) && result.get(fieldName).isObject() && updateValue.isObject()) {
                    result.set(fieldName, mergeJsonNodes(result.get(fieldName), updateValue, replaceNodeNames));
                } else {
                    // Otherwise, update or add the field
                    result.set(fieldName, updateValue);
                }
            }
            return result;
        } else if (mainNode.isArray() && updateNode.isArray()) {
            // If both nodes are arrays, concatenate them
            ArrayNode result = ((ArrayNode) mainNode).deepCopy();
            for (JsonNode element : updateNode) {
                result.add(element);
            }
            return result;
        } else {
            // For other types, the update node takes precedence
            return updateNode;
        }
    }

    /**
     * Hashes a string value using the MD5 algorithm.
     * This method converts the input string to an MD5 hash and returns it as a 32-character hexadecimal string.
     *
     * @param input the string to hash
     * @return the MD5 hash of the input string as a hexadecimal string, or null if an error occurs
     */
    public static String hashMD5(String input) {
        if (input == null) {
            return null;
        }

        try {
            // Get an instance of the MD5 message digest
            MessageDigest md = MessageDigest.getInstance("MD5");

            // Convert the input string to bytes using UTF-8 encoding and update the digest
            md.update(input.getBytes(StandardCharsets.UTF_8));

            // Get the hash bytes
            byte[] digest = md.digest();

            // Convert the byte array to a hexadecimal string
            BigInteger bigInt = new BigInteger(1, digest);
            String hashText = bigInt.toString(16);

            // Pad with leading zeros to ensure it's 32 characters
            while (hashText.length() < 32) {
                hashText = "0" + hashText;
            }

            return hashText;
        } catch (NoSuchAlgorithmException e) {
            log.error("Error hashing string to MD5", e);
            return null;
        }
    }
}
