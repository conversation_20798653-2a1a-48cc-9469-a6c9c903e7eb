package vn.vinclub.voucher.dto.event.internal;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import vn.vinclub.voucher.dto.event.BaseEvent;
import vn.vinclub.voucher.enums.VoucherListingStatusEnum;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProviderVoucherListingStatusChangeEvent implements BaseEvent {
    public static final String EVENT_CODE = "PROVIDER_VOUCHER_LISTING_STATUS_CHANGE";

    private Long providerId;
    private String providerCode;
    private Long voucherListingId;
    private VoucherListingStatusEnum oldStatus;
    private VoucherListingStatusEnum newStatus;
    private Boolean voucherDetailChanged;

    @Override
    public String getKafkaMessageKey() {
        return providerCode;
    }

    @Override
    public String getEventCode() {
        return EVENT_CODE;
    }
}
