package vn.vinclub.voucher.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.io.Serializable;

@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ChangeMsgDto implements Serializable {
    @JsonProperty("id")
    private String msgKey;

    @JsonProperty("value")
    private Long id;

    @JsonProperty("extraValue")
    private String code;
}
