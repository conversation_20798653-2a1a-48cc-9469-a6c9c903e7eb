package vn.vinclub.voucher.dto.provider.voucher;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import vn.vinclub.voucher.enums.ApplyChannelEnum;
import vn.vinclub.voucher.enums.PromotionTypeEnum;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProviderVoucherListingDetailsDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long providerId;
    private String providerCode;
    private Long voucherListingId;
    private String providerMerchantName;
    private Map<String, String> voucherNames;
    private Integer quantity;
    private BigDecimal price;
    private Long voucherDiscountValue;
    private PromotionTypeEnum promotionType;
    private Long basePurchasePoint;
    private Long expiryTimeInMs;
    private ApplyChannelEnum applyChannel;
    private Map<String, String> voucherImages;
    private String merchantImageUrl;
    private String voucherImageUrl;
    private Map<String, String> voucherDescriptions;
    private Map<String, String> voucherTermAndConditions;
    private List<StoreApply> storeApplies;


    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class StoreApply implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private String storeName;
        private String address;
        private String latitude;
        private String longitude;
    }
}
