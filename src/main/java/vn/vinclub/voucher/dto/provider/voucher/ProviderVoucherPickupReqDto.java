package vn.vinclub.voucher.dto.provider.voucher;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldNameConstants;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@FieldNameConstants
public class ProviderVoucherPickupReqDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 2451403461944636724L;

    @NotBlank(message = "voucherImportId is required")
    private String voucherImportId;

    @NotNull(message = "quantity is required")
    private Integer quantity;
    private JsonNode metadata;

    // enhance
    private Long providerId;
    private String providerCode;
    private Long voucherListingId;
    private String providerVoucherListingId;

    @JsonIgnore
    private Integer currentQuantity;
}
