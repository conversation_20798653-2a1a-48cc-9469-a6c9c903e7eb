package vn.vinclub.voucher.dto.client.urbox.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.apache.commons.collections4.MapUtils;
import org.springframework.util.StringUtils;
import vn.vinclub.common.util.JsonUtils;
import vn.vinclub.voucher.constant.AppConst;
import vn.vinclub.voucher.enums.ApplyChannelEnum;
import vn.vinclub.voucher.enums.PromotionTypeEnum;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true, value = {"voucherDescriptions", "voucherTermAndConditions", "voucherNames"})
public class UrBoxGiftDto {
    private String id;
    private String brand;
    private String brandId;
    private String codeDisplay;
    private Integer codeDisplayType;
    private String catId;
    private String giftId;
    private String title;
    private String type;
    private String price;
    private String point;
    private String valuex;
    private String weight;
    @JsonProperty("justGetOrder")
    private String justGetOrder;
    private String view;
    private String quantity;
    private Integer stock;
    private Integer usageCheck;
    private String codeQuantity;
    private String image;
    private JsonNode images;
    private JsonNode imagesRectangle;
    private String expireDuration;
    private Integer pricePromo;
    private Integer startPromo;
    private Integer endPromo;
    private Integer isPromo;
    private String isUnfix;
    private String parentCatId;
    private String brandOnline;
    @JsonProperty("brandImage")
    private String brandImage;
    private String brandLogoLoyalty;
    private String brandName;
    private String content;
    private String note;
    private List<Office> office;

    // enhanced fields
    private Map<String, String> voucherDescriptions;
    private Map<String, String> voucherTermAndConditions;
    private Map<String, String> voucherNames;

    public void mergeWithEnglishDto(UrBoxGiftDto engDto) {
        if (this.voucherDescriptions == null) {
            this.voucherDescriptions = new HashMap<>();
            this.voucherDescriptions.put(AppConst.Language.VI, content);
        }
        if (this.voucherTermAndConditions == null) {
            this.voucherTermAndConditions = new HashMap<>();
            this.voucherTermAndConditions.put(AppConst.Language.VI, note);
        }
        if (this.voucherNames == null) {
            this.voucherNames = new HashMap<>();
            this.voucherNames.put(AppConst.Language.VI, title);
        }
        if (engDto != null) {
            this.voucherDescriptions.put(AppConst.Language.EN, Optional.ofNullable(engDto.getContent()).filter(StringUtils::hasText).orElse(this.content));
            this.voucherTermAndConditions.put(AppConst.Language.EN, Optional.ofNullable(engDto.getNote()).filter(StringUtils::hasText).orElse(this.note));
            this.voucherNames.put(AppConst.Language.EN, Optional.ofNullable(engDto.getTitle()).filter(StringUtils::hasText).orElse(this.title));
        }
    }

    @JsonIgnore
    public Long getExpiryTimeInMs() {
        if (this.expireDuration == null) {
            return 0L;
        }
        String[] durationParts = this.expireDuration.split(" ");
        if (durationParts.length == 2) {
            try {
                int duration = Integer.parseInt(durationParts[0]);
                String unit = durationParts[1].toLowerCase();
                return switch (unit) {
                    case "months", "tháng" -> (long) duration * 30 * 24 * 60 * 60 * 1000;
                    case "days", "ngày" -> (long) duration * 24 * 60 * 60 * 1000;
                    case "hours", "giờ" -> (long) duration * 60 * 60 * 1000;
                    case "minutes", "phút" -> (long) duration * 60 * 1000;
                    default -> 0L;
                };
            } catch (NumberFormatException e) {
                return 0L;
            }
        } else {
            return 0L;
        }
    }

    @JsonIgnore
    public PromotionTypeEnum getPromotionType() {
        if ("1".equals(this.type)) {
            return PromotionTypeEnum.MONEY_REDUCTION;
        }

        if ("11".equals(this.type)) {
            return PromotionTypeEnum.PERCENTAGE_REDUCTION;
        }

        return PromotionTypeEnum.UNCATEGORIZED;
    }

    @JsonIgnore
    public ApplyChannelEnum getApplyChannel() {
        if ("1".equals(this.brandOnline)) {
            return ApplyChannelEnum.OFFLINE;
        }
        if ("2".equals(this.brandOnline)) {
            return ApplyChannelEnum.ONLINE;
        }
        return ApplyChannelEnum.UNKNOWN;
    }

    @JsonIgnore
    public String getCurrency() {
        return "VND";
    }

    @JsonIgnore
    public Map<String, String> getVoucherImages() {
        if (Objects.isNull(this.images) || this.images.isEmpty() || this.images.isNull()) {
            return Collections.emptyMap();
        }
        var map = JsonUtils.readMap(this.images);
        if (MapUtils.isEmpty(map)) {
            return Collections.emptyMap();
        }
        return map.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().toString()
                ));
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @SuperBuilder
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Office {
        private String address;
        private String addressEn;
        private String cityId;
        private String latitude;
        private String longitude;
        private String brandId;
        private String districtId;
        private String wardId;
        private String code;
        private String number;
        private String phone;
        private String geo;
        @JsonProperty("isApply")
        private String isApply;
        private String id;
        private String titleCity;
    }

    @JsonIgnore
    public String getDisplayHashData() {
        StringBuilder sb = new StringBuilder();
        sb.append(this.id).append("|");
        sb.append(this.brandName).append("|");
        sb.append(this.title).append("|");
        sb.append(this.image).append("|");
        sb.append(JsonUtils.toString(this.content)).append("|");
        sb.append(JsonUtils.toString(this.note)).append("|");
        sb.append(JsonUtils.toString(this.office));
        return sb.toString();
    }
}