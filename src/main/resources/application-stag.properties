# Spring Boot application properties for STAGING environment
spring.application.name=vclub-voucher-service
server.port=8080
server.servlet.context-path=/vr
spring.main.allow-bean-definition-overriding=true

# logging
logging.config=classpath:logback-spring.xml
spring.main.banner-mode=off

# server
server.max-http-request-header-size=2MB
server.servlet.encoding.charset=UTF-8
server.servlet.session.tracking-modes=url

########## PostgreSQL - connection
spring.datasource.url=${POSTGRESQL_URL}
spring.datasource.username=${POSTGRESQL_USER}
spring.datasource.password=${POSTGRESQL_PASSWORD}
spring.datasource.hikari.minimumIdle=2
spring.datasource.hikari.maximumPoolSize=3
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.poolName=SpringBootJPAHikariCP
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000

# The SQL dialect makes Hibernate generate better SQL for the chosen database
#spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.PostgreSQLDialect
#drop n create table again, good for testing, comment this in production
# Hibernate ddl auto (create, create-drop, validate, update, none)
spring.jpa.properties.hibernate.default_schema=${DB_DEFAULT_SCHEMA}
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.show-sql=false
#logging.level.org.hibernate.SQL=DEBUG

# REDDISON
redisson.config.address=${REDISSON_CONFIG_ADDRESS}
redisson.config.password=${REDISSON_CONFIG_PASSWORD:}
redisson.config.database=${REDISSON_CONFIG_DATABASE:0}
redisson.config.response.timeout=${REDISSON_CONFIG_RESPONSE_TIMEOUT:3000}
redisson.config.connection.timeout=${REDISSON_CONFIG_CONNECTION_TIMEOUT:3000}
redisson.config.connection.idle.time=${REDISSON_CONFIG_CONNECTION_IDLE_TIME:300000}
redisson.config.connection.keep-alive=${REDISSON_CONFIG_CONNECTION_KEEP_ALIVE:true}
redisson.config.connection.max=${REDISSON_CONFIG_CONNECTION_MAX:64}
redisson.config.connection.min=${REDISSON_CONFIG_CONNECTION_MIN:24}

#kafka configuration
kafka.properties.sasl.mechanism=${KAFKA_SASL_MECHANISM:PLAIN}
kafka.properties.sasl.jaas.config=${KAFKA_SASL_JAAS_CONFIG:org.apache.kafka.common.security.plain.PlainLoginModule required username="vhm-user" password="vhm-pass";}
kafka.properties.bootstrap.servers=${KAFKA_BOOTSTRAP_SERVER:localhost:9092}
kafka.properties.security.protocol=${KAFKA_SECURITY_PROTOCOL:PLAINTEXT}
kafka.properties.basic.auth.credentials.source=USER_INFO
kafka.properties.schema.registry.basic.auth.user.info=${KAFKA_REGISTRY_USERNAME:}:${KAFKA_REGISTRY_PASSWORD:}
kafka.properties.schema.registry.url=${KAFKA_REGISTRY_URL:}
kafka.properties.max-poll-records=${KAFKA_MAX_POLL_RECORDS:10}
kafka.properties.auto.offset.reset=${KAFKA_AUTO_OFFSET_RESET:}
kafka.properties.main.concurrency=${KAFKA_MAIN_CONCURRENCY:2}

# Rest logging
vinclub.logging.level.client.RestTemplate=${REST_LOGGING_LEVEL:DEBUG}

# swagger
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
logging.level.io.swagger.models.parameters.AbstractSerializableParameter=error

# redisson channel
redis.channel.change=${REDISSON_CHANNEL_CHANGE}

# kafka topics
kafka.provider_voucher_purchase_request.topic.name=voucher_svc.provider_voucher_purchase_request
kafka.provider_voucher_purchase_result.topic.name=voucher_svc.provider_voucher_purchase_result
kafka.provider_voucher_listing_status_change.topic.name=voucher_svc.provider_voucher_listing_status_change
kafka.purchased_voucher_status_change.topic.name=voucher_svc.purchased_voucher_status_change

# kafka groups
kafka.properties.main.group.name=voucher-service.consumer.main_${spring.profiles.active}
kafka.provider_voucher_purchase_request.group.name=voucher-service.consumer.provider_voucher_purchase_request_${spring.profiles.active}
kafka.provider_voucher_listing_status_change.group.name=voucher-service.consumer.provider_voucher_listing_status_change_${spring.profiles.active}

# money to point conversion
vclub_exchange_rate.unit.amount=${EXCHANGE_MONEY_UNIT_AMOUNT}
vclub_exchange_rate.point.per.unit.amount=${POINT_PER_MONEY_UNIT_AMOUNT}

# Provider Urbox
provider_integration.urbox.provider_code=${PROVIDER_INTEGRATION_URBOX_PROVIDER_CODE}
provider_integration.urbox.api.url=${PROVIDER_INTEGRATION_URBOX_API_URL}
provider_integration.urbox.app.id=${PROVIDER_INTEGRATION_URBOX_APP_ID}
provider_integration.urbox.app.secret=${PROVIDER_INTEGRATION_URBOX_APP_SECRET}
provider_integration.urbox.campaign.default=${PROVIDER_INTEGRATION_URBOX_CAMPAIGN_DEFAULT}
provider_integration.urbox.max-quantity-per-transaction=${PROVIDER_INTEGRATION_URBOX_MAX_QUANTITY_PER_TRANSACTION}
provider_integration.urbox.connect_timeout=${PROVIDER_INTEGRATION_URBOX_CONNECT_TIMEOUT:3000}
provider_integration.urbox.response_timeout=${PROVIDER_INTEGRATION_URBOX_RESPONSE_TIMEOUT:5000}
provider_integration.urbox.max_connections=${PROVIDER_INTEGRATION_URBOX_MAX_CONNECTIONS:100}
